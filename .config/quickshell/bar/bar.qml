import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import "./modules"
import "./modules/visualizerModules"

PanelWindow {
    id: window

    property var targetScreen: Quickshell.screens[0]
    property int screenIndex: 0
    property bool showMprisOverlay: false
    property bool showCalendarOverlay: false
    property var timeModuleItem: null

    screen: targetScreen

    Theme {
        id: theme
    }

    property var workspaceRanges: [
        [1, 6],
        [7, 9]
    ]

    property var screenConfigs: [
        {
            left: ["workspaces"],
            center: ["time", "mpris"],
            right: ["network", "volume", "notifications", "system"]
        },
        {
            left: ["workspaces"],
            center: ["time"],
            right: ["network", "volume", "notifications", "system"]
        }
    ]

    property var availableModules: {
        "workspaces": "HyprlandWorkspaces",
        "time": "Time",
        "system": "SystemIndicators",
        "mpris": "MprisWidget",
        "volume": "VolumeWidget",
        "network": "NetworkWidget",
        "notifications": "NotificationButton"
    }

    property var currentConfig: getScreenConfig(screenIndex)

    anchors {
        left: true
        right: true
        top: true
        bottom: false
    }

    implicitHeight: 30
    visible: true
    color: "transparent"

    Component.onCompleted: {
    }

    Rectangle {
        id: bar
        anchors.fill: parent
        radius: 0
        color: theme.background

        RowLayout {
            anchors.fill: parent
            anchors.leftMargin: 16
            anchors.rightMargin: 16
            anchors.topMargin: 6
            anchors.bottomMargin: 6
            spacing: 8

            Row {
                Layout.alignment: Qt.AlignLeft
                spacing: 10

                Repeater {
                    model: currentConfig.left

                    Loader {
                        source: getModuleSource(modelData)
                        onLoaded: {
                            if (modelData === "workspaces" && item) {
                                item.screenIndex = window.screenIndex
                            }
                        }
                    }
                }
            }

            Item {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.alignment: Qt.AlignHCenter

                Row {
                    anchors.centerIn: parent
                    spacing: 0

                    Text {
                        text: "【"
                        color: theme.accent
                        font.pixelSize: 14
                        font.family: "JetBrains Mono, monospace"
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    Row {
                        id: centerContent
                        spacing: 10
                        anchors.verticalCenter: parent.verticalCenter

                        Repeater {
                            model: currentConfig.center

                            Loader {
                                source: getModuleSource(modelData)
                                onLoaded: {
                                    if (modelData === "workspaces" && item) {
                                        item.screenIndex = window.screenIndex
                                    }
                                    if (modelData === "mpris" && item) {
                                        item.showMprisOverlayRequested.connect(function() {
                                            window.showMprisOverlay = !window.showMprisOverlay
                                        })

                                        item.hideMprisOverlayRequested.connect(function() {
                                            window.showMprisOverlay = false
                                        })
                                    }
                                    if (modelData === "time" && item) {
                                        window.timeModuleItem = item
                                        item.showCalendarOverlayRequested.connect(function() {
                                            window.showCalendarOverlay = !window.showCalendarOverlay
                                        })

                                        item.hideCalendarOverlayRequested.connect(function() {
                                            window.showCalendarOverlay = false
                                        })
                                    }

                                }
                            }
                        }
                    }

                    Text {
                        text: "】"
                        color: theme.accent
                        font.pixelSize: 14
                        font.family: "JetBrains Mono, monospace"
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 0
                visible: window.screenIndex === 0

                Text {
                    text: "【"
                    color: theme.accent
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }

                VisualizerWidget {
                    id: visualizer
                    anchors.verticalCenter: parent.verticalCenter
                }

                Text {
                    text: "】"
                    color: theme.accent
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 0

                Text {
                    text: "【"
                    color: theme.accent
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }

                Row {
                    id: rightContent
                    spacing: 0
                    anchors.verticalCenter: parent.verticalCenter

                    Repeater {
                        model: currentConfig.right

                        Loader {
                            source: getModuleSource(modelData)
                            onLoaded: {
                                if (modelData === "workspaces" && item) {
                                    item.screenIndex = window.screenIndex
                                }

                            }
                        }
                    }
                }

                Text {
                    text: "】"
                    color: theme.accent
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
        }
    }

    Loader {
        id: mprisOverlayLoader
        source: "mprisOverlay.qml"
        onLoaded: {
            item.screen = window.targetScreen
            item.barWindow = window
            item.overlayVisible = Qt.binding(() => window.showMprisOverlay)
        }
    }

    Loader {
        id: calendarOverlayLoader
        source: "calendarOverlay.qml"
        onLoaded: {
            item.screen = window.targetScreen
            item.barWindow = window
            item.overlayVisible = Qt.binding(() => window.showCalendarOverlay)
        }
    }



    function getScreenConfig(screenIndex) {
        if (screenIndex < screenConfigs.length) {
            return screenConfigs[screenIndex]
        }
        return {
            left: ["workspaces"],
            center: ["time", "mpris"],
            right: ["system"]
        }
    }

    function getWorkspaceRange(screenIndex) {
        if (screenIndex < workspaceRanges.length) {
            return workspaceRanges[screenIndex]
        }
        return [1, 10]
    }

    function getModuleSource(moduleName) {
        var moduleFileMap = {
            "workspaces": "hyprlandWorkspaces",
            "time": "time",
            "system": "systemIndicators",
            "mpris": "mprisWidget",
            "volume": "volumeWidget",
            "network": "networkWidget",
            "notifications": "notificationButton"
        }
        var fileName = moduleFileMap[moduleName]
        if (fileName) {
            return "./modules/" + fileName + ".qml"
        }
        return ""
    }
}
