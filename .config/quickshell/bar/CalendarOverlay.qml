import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import "./modules"

PanelWindow {
    id: calendarOverlay

    property var screen
    property var barWindow
    property bool overlayVisible: false

    screen: Quickshell.screens[0]

    visible: overlayVisible
    color: "transparent"

    width: 320
    height: 300

    anchors {
        top: true
        left: false
        right: false
        bottom: false
    }

    WlrLayershell.layer: WlrLayer.Overlay

    function updatePosition() {
        if (screen && screen.geometry && barWindow && barWindow.timeModuleItem) {
            var screenRect = screen.geometry
            var barRect = barWindow.geometry
            var timeModule = barWindow.timeModuleItem

            var timeModuleGlobalX = barRect.x + timeModule.x + (timeModule.width / 2)
            var overlayX = timeModuleGlobalX - 160

            x = Math.max(10, Math.min(overlayX, screenRect.width - 320 - 10))
            y = barRect.height + 4
        } else {
            x = 200
            y = 30
        }
    }

    Theme {
        id: theme
    }

    Component.onCompleted: updatePosition()

    onBarWindowChanged: updatePosition()
    onScreenChanged: updatePosition()

    Item {
        id: calendarContainer
        anchors.fill: parent

        Canvas {
            id: uShapeCanvas
            anchors.fill: parent
            opacity: overlayVisible ? 1.0 : 0.0

            onPaint: {
                var ctx = getContext("2d")
                ctx.clearRect(0, 0, width, height)

                ctx.fillStyle = "#000000"
                ctx.strokeStyle = "transparent"
                ctx.lineWidth = 0

                var cornerRadius = 16
                var notchWidth = 40
                var notchHeight = 8
                var notchX = width / 2 - notchWidth / 2

                ctx.beginPath()
                ctx.moveTo(cornerRadius, notchHeight)
                ctx.lineTo(notchX, notchHeight)
                ctx.lineTo(notchX + notchWidth / 2, 0)
                ctx.lineTo(notchX + notchWidth, notchHeight)
                ctx.lineTo(width - cornerRadius, notchHeight)
                ctx.arcTo(width, notchHeight, width, notchHeight + cornerRadius, cornerRadius)
                ctx.lineTo(width, height - cornerRadius)
                ctx.arcTo(width, height, width - cornerRadius, height, cornerRadius)
                ctx.lineTo(cornerRadius, height)
                ctx.arcTo(0, height, 0, height - cornerRadius, cornerRadius)
                ctx.lineTo(0, notchHeight + cornerRadius)
                ctx.arcTo(0, notchHeight, cornerRadius, notchHeight, cornerRadius)
                ctx.closePath()
                ctx.fill()
            }

            Behavior on opacity {
                NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
            }
        }

        CalendarWidget {
            anchors.fill: parent
            anchors.margins: 16
            anchors.topMargin: 24
        }

        MouseArea {
            anchors.fill: parent
            onClicked: {
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        z: -1
        onClicked: {
            overlayVisible = false
        }
    }

    Timer {
        id: autoHideTimer
        interval: 5000
        onTriggered: {
            calendarOverlay.overlayVisible = false
        }
    }

    onOverlayVisibleChanged: {
        if (overlayVisible) {
            updatePosition()
            uShapeCanvas.requestPaint()
        }
    }
}
