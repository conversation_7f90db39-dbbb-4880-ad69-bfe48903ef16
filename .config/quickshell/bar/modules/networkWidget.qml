import QtQuick
import QtQuick.Layouts

Rectangle {
    id: networkWidget
    width: Math.min(expanded ? expandedWidth : collapsedWidth, maxWidth)
    height: 18
    radius: 9
    color: "transparent"
    clip: true

    property bool expanded: false
    property int collapsedWidth: 32
    property int expandedWidth: 100
    property int maxWidth: 100

    property string connectionType: "ethernet"
    property string networkIcon: getNetworkIcon()
    property real downloadSpeed: 2.5 * 1024
    property real uploadSpeed: 180

    function getNetworkIcon() {
        if (connectionType === "ethernet") {
            return "󰈀"
        } else if (connectionType === "wifi") {
            return "󰤨"
        } else {
            return "󰤭"
        }
    }

    Theme {
        id: theme
    }

    Timer {
        id: collapseTimer
        interval: 3000
        onTriggered: expanded = false
    }

    Component.onCompleted: {
    }

    onExpandedChanged: {
        if (expanded) {
            collapseTimer.start()
        } else {
            collapseTimer.stop()
        }
    }



    function formatBytes(bytes) {
        if (bytes < 1024) return bytes.toFixed(0) + " B/s"
        else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + " KB/s"
        else return (bytes / (1024 * 1024)).toFixed(1) + " MB/s"
    }

    RowLayout {
        anchors.fill: parent
        anchors.leftMargin: 6
        anchors.rightMargin: 6
        spacing: 0

        Text {
            id: networkIconText
            text: networkWidget.networkIcon
            color: connectionType === "disconnected" ? theme.textSecondary : theme.accent
            font.pixelSize: 12
            font.family: "JetBrains Mono Nerd Font, monospace"
            Layout.alignment: Qt.AlignVCenter
        }

        Row {
            id: bandwidthContent
            visible: expanded && connectionType !== "disconnected"
            spacing: 2
            Layout.alignment: Qt.AlignVCenter

            Text {
                text: "󰇚"
                color: "#99ffdd"
                font.pixelSize: 8
                font.family: "JetBrains Mono Nerd Font, monospace"
            }

            Text {
                text: formatBytes(downloadSpeed)
                color: "#99ffdd"
                font.pixelSize: 8
                font.family: "JetBrains Mono, monospace"
            }

            Text {
                text: "󰕒"
                color: "#ffcc66"
                font.pixelSize: 8
                font.family: "JetBrains Mono Nerd Font, monospace"
            }

            Text {
                text: formatBytes(uploadSpeed)
                color: "#ffcc66"
                font.pixelSize: 8
                font.family: "JetBrains Mono, monospace"
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onClicked: {
            expanded = !expanded
        }
        onEntered: {
            if (expanded) {
                collapseTimer.stop()
            }
        }
        onExited: {
            if (expanded) {
                collapseTimer.start()
            }
        }
    }

    Behavior on width {
        NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
    }

}
