import QtQuick
import Quickshell.Services.Notifications

NotificationServer {
    id: notificationService
    
    bodySupported: true
    imageSupported: true
    actionsSupported: true
    persistenceSupported: true
    keepOnReload: true
    
    property var activeNotifications: []
    property var notificationQueue: []
    property int maxVisibleNotifications: 5
    
    signal notificationReceived(var notification)
    signal notificationExpired(var notification)
    signal notificationDismissed(var notification)
    
    onNotification: function(notification) {
        notification.tracked = true
        
        if (activeNotifications.length >= maxVisibleNotifications) {
            notificationQueue.push(notification)
        } else {
            activeNotifications.push(notification)
            notificationReceived(notification)
        }
        
        if (notification.expireTimeout > 0) {
            var timer = Qt.createQmlObject(`
                import QtQuick
                Timer {
                    interval: ${notification.expireTimeout * 1000}
                    running: true
                    repeat: false
                    onTriggered: {
                        notificationService.expireNotification(${notification.id})
                        destroy()
                    }
                }
            `, notificationService, "expireTimer")
        }
    }
    
    function expireNotification(notificationId) {
        for (var i = 0; i < activeNotifications.length; i++) {
            if (activeNotifications[i].id === notificationId) {
                var notification = activeNotifications[i]
                activeNotifications.splice(i, 1)
                notification.expire()
                notificationExpired(notification)
                processQueue()
                break
            }
        }
    }
    
    function dismissNotification(notificationId) {
        for (var i = 0; i < activeNotifications.length; i++) {
            if (activeNotifications[i].id === notificationId) {
                var notification = activeNotifications[i]
                activeNotifications.splice(i, 1)
                notification.dismiss()
                notificationDismissed(notification)
                processQueue()
                break
            }
        }
    }
    
    function processQueue() {
        if (notificationQueue.length > 0 && activeNotifications.length < maxVisibleNotifications) {
            var notification = notificationQueue.shift()
            activeNotifications.push(notification)
            notificationReceived(notification)
        }
    }
    
    function getUrgencyColor(urgency) {
        switch (urgency) {
            case NotificationUrgency.Low:
                return "#42be65"
            case NotificationUrgency.Critical:
                return "#ff7eb6"
            case NotificationUrgency.Normal:
            default:
                return "#33b1ff"
        }
    }
    
    function getDefaultIcon() {
        return "../icons/pattern-sign.webp"
    }
}
