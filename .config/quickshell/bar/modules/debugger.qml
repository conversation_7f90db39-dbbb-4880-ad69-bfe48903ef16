import QtQuick
import QtQuick.Layouts
import Quickshell.Services.Mpris
import Quickshell.Services.Pipewire

QtObject {
    id: debugHelper
    
    property bool enableMprisDebug: false
    property bool enableVolumeDebug: false
    property bool enableWorkspaceDebug: false
    
    function logMpris(message) {
        if (enableMprisDebug) {
            console.log("MPRIS DEBUG:", message)
        }
    }
    
    function logVolume(message) {
        if (enableVolumeDebug) {
            console.log("VOLUME DEBUG:", message)
        }
    }
    
    function logWorkspace(message) {
        if (enableWorkspaceDebug) {
            console.log("WORKSPACE DEBUG:", message)
        }
    }
    
    function logMprisPlayers() {
        if (!enableMprisDebug) return
        
        if (!Mpris.players || !Mpris.players.values) {
            logMpris("No players or values available")
            return
        }
        
        var playersList = Mpris.players.values
        logMpris("Found " + playersList.length + " players")
        
        for (var i = 0; i < playersList.length; i++) {
            var player = playersList[i]
            logMpris("Player " + i + " identity: " + (player ? player.identity : "null") + 
                    " state: " + (player ? player.playbackState : "null"))
        }
    }
    
    function logVolumeState() {
        if (!enableVolumeDebug) return
        
        var audioSink = Pipewire.defaultAudioSink
        var audioNode = audioSink ? audioSink.audio : null
        
        logVolume("Audio sink: " + (audioSink ? audioSink.description : "none"))
        logVolume("Volume: " + (audioNode ? Math.round(audioNode.volume * 100) + "%" : "unknown"))
        logVolume("Muted: " + (audioNode ? audioNode.muted : "unknown"))
    }
    
    Timer {
        id: mprisDebugTimer
        interval: 2000
        running: enableMprisDebug
        repeat: true
        onTriggered: {
            debugHelper.logMprisPlayers()
        }
    }
    
    Timer {
        id: volumeDebugTimer
        interval: 1000
        running: enableVolumeDebug
        repeat: true
        onTriggered: {
            debugHelper.logVolumeState()
        }
    }
}
