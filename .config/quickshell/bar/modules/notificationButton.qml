import QtQuick
import Quickshell.Services.Notifications

Rectangle {
    id: notificationButton
    
    property bool showNotifications: false
    property int notificationCount: 0
    
    Theme {
        id: theme
    }
    
    width: 40
    height: parent.height
    color: "transparent"
    
    Rectangle {
        id: buttonBackground
        anchors.centerIn: parent
        width: 32
        height: 32
        radius: 6
        color: notificationButton.showNotifications ? theme.accent : "transparent"
        border.color: theme.accent
        border.width: 1
        
        Behavior on color {
            ColorAnimation { duration: 200 }
        }
        
        AnimatedSigilBackground {
            anchors.fill: parent
            radius: parent.radius
            visible: notificationButton.notificationCount > 0
            sigilSource: "../icons/pattern-sign.webp"
            animationSpeed: 2000
            overlayColor: "#000000"
            overlayOpacity: 0.6
        }
        
        Text {
            anchors.centerIn: parent
            text: "🔔"
            font.pixelSize: 16
            color: theme.text
        }
        
        Rectangle {
            visible: notificationButton.notificationCount > 0
            anchors {
                top: parent.top
                right: parent.right
                topMargin: -4
                rightMargin: -4
            }
            width: 16
            height: 16
            radius: 8
            color: theme.accent
            border.color: theme.background
            border.width: 1
            
            Text {
                anchors.centerIn: parent
                text: notificationButton.notificationCount > 9 ? "9+" : notificationButton.notificationCount.toString()
                font.pixelSize: 8
                font.bold: true
                color: "#ffffff"
            }
        }
    }
    
    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        
        onClicked: {
            notificationButton.showNotifications = !notificationButton.showNotifications
        }
        
        onEntered: {
            buttonBackground.color = Qt.lighter(theme.accent, 1.2)
        }
        
        onExited: {
            buttonBackground.color = notificationButton.showNotifications ? theme.accent : "transparent"
        }
    }
}
