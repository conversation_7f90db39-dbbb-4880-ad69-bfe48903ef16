import QtQuick
import Quickshell
import Quickshell.Services.Notifications

PopupWindow {
    id: notificationBar

    property var targetScreen: Quickshell.screens[0]
    property int maxNotifications: 5
    property int notificationSpacing: 10
    property int sideMargin: 20

    screen: targetScreen
    anchor {
        rect.x: targetScreen.geometry.width - width - sideMargin
        rect.y: sideMargin
        rect.width: width
        rect.height: height
    }

    width: 420
    height: Math.min(notificationColumn.implicitHeight, targetScreen.geometry.height - (sideMargin * 2))

    visible: notificationRepeater.count > 0

    color: "transparent"

    NotificationServer {
        id: notificationServer

        bodySupported: true
        imageSupported: true
        actionsSupported: true
        persistenceSupported: true
        keepOnReload: true

        onNotification: function(notification) {
            notification.tracked = true
            notificationModel.append({"notificationData": notification})

            if (notificationModel.count > maxNotifications) {
                notificationModel.remove(0)
            }
        }
    }
    
    ListModel {
        id: notificationModel
    }
    

    
    Rectangle {
        anchors.fill: parent
        color: "transparent"
        
        Column {
            id: notificationColumn
            anchors {
                top: parent.top
                left: parent.left
                right: parent.right
            }
            spacing: notificationSpacing
            
            Repeater {
                id: notificationRepeater
                model: notificationModel
                
                NotificationWidget {
                    width: notificationColumn.width
                    notification: model.notificationData
                    
                    Component.onCompleted: {
                        slideInAnimation.start()
                    }
                    
                    Component.onDestruction: {
                        slideOutAnimation.start()
                    }
                    
                    transform: Translate {
                        id: slideTransform
                        x: 400
                    }
                    
                    NumberAnimation {
                        id: slideInAnimation
                        target: slideTransform
                        property: "x"
                        from: 400
                        to: 0
                        duration: 400
                        easing.type: Easing.OutCubic
                    }
                    
                    NumberAnimation {
                        id: slideOutAnimation
                        target: slideTransform
                        property: "x"
                        from: 0
                        to: 400
                        duration: 300
                        easing.type: Easing.InCubic
                    }
                }
            }
        }
    }
    
    Timer {
        id: cleanupTimer
        interval: 100
        repeat: true
        running: true
        
        onTriggered: {
            for (var i = notificationModel.count - 1; i >= 0; i--) {
                var notification = notificationModel.get(i).notificationData
                if (!notification || !notification.tracked) {
                    notificationModel.remove(i)
                }
            }
        }
    }
}
