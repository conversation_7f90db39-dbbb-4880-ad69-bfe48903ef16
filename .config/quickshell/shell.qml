//@ pragma UseQApplication
import QtQuick
import Quickshell
import "bar"
import "bar/modules"

ShellRoot {
    Bar {
        targetScreen: Quickshell.screens[0]
        screenIndex: 0
    }

    Bar {
        targetScreen: Quickshell.screens.length > 1 ? Quickshell.screens[1] : Quickshell.screens[0]
        screenIndex: 1
    }

    Loader {
        id: debuggerLoader
        source: "bar/modules/debugger.qml"
    }

    Repeater {
        model: Quickshell.screens

        NotificationBar {
            targetScreen: modelData
        }
    }
}
