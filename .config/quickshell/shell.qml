//@ pragma UseQApplication
import QtQuick
import Quickshell
import Quickshell.Services.Notifications
import "bar"
import "bar/modules"

ShellRoot {
    Bar {
        targetScreen: Quickshell.screens[0]
        screenIndex: 0
    }

    Bar {
        targetScreen: Quickshell.screens.length > 1 ? Quickshell.screens[1] : Quickshell.screens[0]
        screenIndex: 1
    }

    Loader {
        id: debuggerLoader
        source: "bar/modules/debugger.qml"
    }

    NotificationServer {
        id: globalNotificationServer

        bodySupported: true
        imageSupported: true
        actionsSupported: true
        persistenceSupported: true
        keepOnReload: true

        onNotification: function(notification) {
            notification.tracked = true
            console.log("Received notification:", notification.summary, notification.body)
        }
    }

    Repeater {
        model: Quickshell.screens

        NotificationBar {
            targetScreen: modelData
        }
    }
}
